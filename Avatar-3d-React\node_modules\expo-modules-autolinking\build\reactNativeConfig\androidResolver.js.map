{"version": 3, "file": "androidResolver.js", "sourceRoot": "", "sources": ["../../src/reactNativeConfig/androidResolver.ts"], "names": [], "mappings": ";;;;;AAcA,0FAuEC;AAKD,sDAoBC;AAKD,4EAqBC;AAID,kEAwBC;AAED,sDA+BC;AAED,wEAmBC;AAiBD,gEA4BC;AAvQD,2DAA6B;AAC7B,+BAA4B;AAC5B,gDAAwB;AAMxB,4CAIsB;AAEf,KAAK,UAAU,uCAAuC,CAC3D,WAAmB,EACnB,iBAA+E;IAE/E,IAAI,iBAAiB,KAAK,IAAI,EAAE,CAAC;QAC/B,qCAAqC;QACrC,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,SAAS,GAAG,iBAAiB,EAAE,SAAS,IAAI,SAAS,CAAC;IAC5D,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IACrD,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,0BAA0B,CAAC,EAAE,UAAU,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAC/F,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,WAAW,GACf,iBAAiB,EAAE,WAAW,IAAI,CAAC,MAAM,qBAAqB,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;IAChG,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,sBAAsB,GAAG,MAAM,gCAAgC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;IAC/F,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,kBAAE,CAAC,QAAQ,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IAClG,MAAM,iBAAiB,GACrB,iBAAiB,EAAE,iBAAiB,IAAI,UAAU,WAAW,IAAI,sBAAsB,GAAG,CAAC;IAC7F,MAAM,eAAe,GAAG,iBAAiB,EAAE,eAAe,IAAI,OAAO,sBAAsB,IAAI,CAAC;IAChG,MAAM,UAAU,GAAG,iBAAiB,EAAE,UAAU,IAAI,EAAE,CAAC;IACvD,MAAM,uBAAuB,GAAG,iBAAiB,EAAE,uBAAuB,CAAC;IAC3E,MAAM,WAAW,GACf,iBAAiB,EAAE,WAAW,IAAI,CAAC,MAAM,qBAAqB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;IAC3F,MAAM,oBAAoB,GACxB,iBAAiB,EAAE,oBAAoB;QACvC,CAAC,MAAM,8BAA8B,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC;IACnE,IAAI,cAAc,GAAG,iBAAiB,EAAE,cAAc;QACpD,CAAC,CAAC,cAAI,CAAC,IAAI,CAAC,UAAU,EAAE,iBAAiB,EAAE,cAAc,CAAC;QAC1D,CAAC,CAAC,cAAI,CAAC,IAAI,CAAC,UAAU,EAAE,mDAAmD,CAAC,CAAC;IAC/E,MAAM,6BAA6B,GAAG,iBAAiB,EAAE,6BAA6B,IAAI,IAAI,CAAC;IAC/F,MAAM,mBAAmB,GAAG,iBAAiB,EAAE,mBAAmB,IAAI,IAAI,CAAC;IAC3E,IAAI,uBAAuB,GAAG,iBAAiB,EAAE,uBAAuB;QACtE,CAAC,CAAC,cAAI,CAAC,IAAI,CAAC,UAAU,EAAE,iBAAiB,EAAE,uBAAuB,CAAC;QACnE,CAAC,CAAC,IAAI,CAAC;IACT,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QACjC,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACpD,IAAI,uBAAuB,EAAE,CAAC;YAC5B,uBAAuB,GAAG,uBAAuB,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED,MAAM,MAAM,GAA8B;QACxC,SAAS,EAAE,UAAU;QACrB,iBAAiB;QACjB,eAAe;QACf,uBAAuB;QACvB,UAAU;QACV,WAAW;QACX,oBAAoB;QACpB,cAAc;QACd,6BAA6B;QAC7B,uBAAuB;QACvB,mBAAmB;KACpB,CAAC;IACF,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QACxB,OAAO,MAAM,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;QACpC,OAAO,MAAM,CAAC,uBAAuB,CAAC;IACxC,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,qBAAqB,CACzC,UAAkB,EAClB,YAA2B,EAC3B,UAAyB;IAEzB,IAAI,UAAU,EAAE,CAAC;QACf,MAAM,cAAc,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,cAAI,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,MAAM,CAAC,CAAC;QACpF,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACvE,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IACD,IAAI,YAAY,EAAE,CAAC;QACjB,MAAM,gBAAgB,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,cAAI,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC,EAAE,MAAM,CAAC,CAAC;QACxF,MAAM,KAAK,GAAG,gBAAgB,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACxD,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,gCAAgC,CACpD,WAAmB,EACnB,UAAkB;IAElB,MAAM,OAAO,GAAG,MAAM,IAAA,sCAA0B,EAC9C,uBAAuB,EACvB,2BAA2B,EAC3B,EAAE,GAAG,EAAE,UAAU,EAAE,CACpB,CAAC;IACF,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,+CAA+C;IAC/C,IAAI,MAAM,IAAA,2BAAe,EAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,yBAAyB,CAAC,CAAC,EAAE,CAAC;QAC7E,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,MAAM,IAAA,sCAA0B,EAAC,gBAAgB,EAAE,2BAA2B,EAAE;QACrF,GAAG,EAAE,UAAU;KAChB,CAAC,CAAC;AACL,CAAC;AAED,IAAI,qBAAqB,GAAkB,IAAI,CAAC;AAChD,IAAI,0BAA0B,GAAkB,IAAI,CAAC;AACrD,SAAgB,2BAA2B,CAAC,SAAiB,EAAE,QAAgB;IAC7E,MAAM,YAAY,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;IAEzC,yBAAyB;IACzB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC3B,qBAAqB;YACnB,gFAAgF,CAAC;IACrF,CAAC;IACD,MAAM,iBAAiB,GAAG,YAAY,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;IACpE,IAAI,iBAAiB,EAAE,CAAC;QACtB,OAAO,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;IAED,qCAAqC;IACrC,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAChC,0BAA0B;YACxB,yFAAyF,CAAC;IAC9F,CAAC;IACD,MAAM,sBAAsB,GAAG,YAAY,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC9E,IAAI,sBAAsB,EAAE,CAAC;QAC3B,OAAO,sBAAsB,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAEM,KAAK,UAAU,qBAAqB,CACzC,UAAkB,EAClB,WAAgB;IAEhB,6CAA6C;IAC7C,IAAI,WAAW,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC;QACpC,OAAO,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC;IACxC,CAAC;IAED,MAAM,iBAAiB,GAAG,4BAA4B,CAAC;IACvD,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;IACzD,sCAAsC;IACtC,IAAI,MAAM,IAAA,2BAAe,EAAC,UAAU,CAAC,EAAE,CAAC;QACtC,MAAM,mBAAmB,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAClE,MAAM,KAAK,GAAG,mBAAmB,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAC3D,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IAED,0CAA0C;IAC1C,MAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;IAChE,IAAI,MAAM,IAAA,2BAAe,EAAC,aAAa,CAAC,EAAE,CAAC;QACzC,MAAM,mBAAmB,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QACrE,MAAM,KAAK,GAAG,mBAAmB,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAC3D,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAEM,KAAK,UAAU,8BAA8B,CAClD,WAAmB,EACnB,WAAgB;IAEhB,MAAM,MAAM,GAAG,WAAW,EAAE,aAAa,EAAE,SAAS;QAClD,CAAC,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC;QAC7D,CAAC,CAAC,WAAW,CAAC;IAChB,MAAM,OAAO,GAAG,MAAM,IAAA,oCAAwB,EAC5C,sBAAsB,EACtB,yBAAyB,EACzB;QACE,GAAG,EAAE,MAAM;QACX,MAAM,EAAE,CAAC,oBAAoB,CAAC;KAC/B,CACF,CAAC;IAEF,yGAAyG;IACzG,sDAAsD;IACtD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;AACtC,CAAC;AAED,IAAI,yBAAyB,GAAkB,IAAI,CAAC;AACpD,SAAS,yBAAyB,CAAC,QAAgB,EAAE,QAAgB;IACnE,MAAM,YAAY,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;IAEzC,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAC/B,yBAAyB;YACvB,yFAAyF,CAAC;IAC9F,CAAC;IACD,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC5D,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3C,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,qBAAqB,CAAC;IAC1C,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAEM,KAAK,UAAU,0BAA0B,CAAC,EAC/C,UAAU,EACV,SAAS,GAIV;IACC,MAAM,YAAY,GAAG;QACnB,iBAAiB;QACjB,aAAa;QACb,aAAa;QACb,aAAa;QACb,aAAa;QACb,YAAY;QACZ,2BAA2B;QAC3B,uBAAuB;QACvB,gBAAgB;KACjB,CAAC;IACF,MAAM,aAAa,GAAG,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,yBAAyB,CAAC;IACpF,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;QAC7C,IAAA,WAAI,EAAC,wBAAwB,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;QACzE,IAAA,WAAI,EAAC,aAAa,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;KAC/D,CAAC,CAAC;IACH,MAAM,QAAQ,GACZ,SAAS,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAC5D,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,IAAI,EAAE,QAAQ,EAAE,QAAQ,IAAI,IAAI,EAAE,CAAC;AAChE,CAAC", "sourcesContent": ["import fs from 'fs/promises';\nimport { glob } from 'glob';\nimport path from 'path';\n\nimport type {\n  RNConfigDependencyAndroid,\n  RNConfigReactNativePlatformsConfigAndroid,\n} from './reactNativeConfig.types';\nimport {\n  fileExistsAsync,\n  globMatchFunctorAllAsync,\n  globMatchFunctorFirstAsync,\n} from '../fileUtils';\n\nexport async function resolveDependencyConfigImplAndroidAsync(\n  packageRoot: string,\n  reactNativeConfig: RNConfigReactNativePlatformsConfigAndroid | null | undefined\n): Promise<RNConfigDependencyAndroid | null> {\n  if (reactNativeConfig === null) {\n    // Skip autolinking for this package.\n    return null;\n  }\n  const sourceDir = reactNativeConfig?.sourceDir || 'android';\n  const androidDir = path.join(packageRoot, sourceDir);\n  const { gradle, manifest } = await findGradleAndManifestAsync({ androidDir, isLibrary: true });\n  if (!manifest && !gradle) {\n    return null;\n  }\n\n  const packageName =\n    reactNativeConfig?.packageName || (await parsePackageNameAsync(androidDir, manifest, gradle));\n  if (!packageName) {\n    return null;\n  }\n  const nativePackageClassName = await parseNativePackageClassNameAsync(packageRoot, androidDir);\n  if (!nativePackageClassName) {\n    return null;\n  }\n\n  const packageJson = JSON.parse(await fs.readFile(path.join(packageRoot, 'package.json'), 'utf8'));\n  const packageImportPath =\n    reactNativeConfig?.packageImportPath || `import ${packageName}.${nativePackageClassName};`;\n  const packageInstance = reactNativeConfig?.packageInstance || `new ${nativePackageClassName}()`;\n  const buildTypes = reactNativeConfig?.buildTypes || [];\n  const dependencyConfiguration = reactNativeConfig?.dependencyConfiguration;\n  const libraryName =\n    reactNativeConfig?.libraryName || (await parseLibraryNameAsync(androidDir, packageJson));\n  const componentDescriptors =\n    reactNativeConfig?.componentDescriptors ||\n    (await parseComponentDescriptorsAsync(packageRoot, packageJson));\n  let cmakeListsPath = reactNativeConfig?.cmakeListsPath\n    ? path.join(androidDir, reactNativeConfig?.cmakeListsPath)\n    : path.join(androidDir, 'build/generated/source/codegen/jni/CMakeLists.txt');\n  const cxxModuleCMakeListsModuleName = reactNativeConfig?.cxxModuleCMakeListsModuleName || null;\n  const cxxModuleHeaderName = reactNativeConfig?.cxxModuleHeaderName || null;\n  let cxxModuleCMakeListsPath = reactNativeConfig?.cxxModuleCMakeListsPath\n    ? path.join(androidDir, reactNativeConfig?.cxxModuleCMakeListsPath)\n    : null;\n  if (process.platform === 'win32') {\n    cmakeListsPath = cmakeListsPath.replace(/\\\\/g, '/');\n    if (cxxModuleCMakeListsPath) {\n      cxxModuleCMakeListsPath = cxxModuleCMakeListsPath.replace(/\\\\/g, '/');\n    }\n  }\n\n  const result: RNConfigDependencyAndroid = {\n    sourceDir: androidDir,\n    packageImportPath,\n    packageInstance,\n    dependencyConfiguration,\n    buildTypes,\n    libraryName,\n    componentDescriptors,\n    cmakeListsPath,\n    cxxModuleCMakeListsModuleName,\n    cxxModuleCMakeListsPath,\n    cxxModuleHeaderName,\n  };\n  if (!result.libraryName) {\n    delete result.libraryName;\n  }\n  if (!result.dependencyConfiguration) {\n    delete result.dependencyConfiguration;\n  }\n  return result;\n}\n\n/**\n * Parse the `RNConfigDependencyAndroid.packageName`\n */\nexport async function parsePackageNameAsync(\n  androidDir: string,\n  manifestPath: string | null,\n  gradlePath: string | null\n): Promise<string | null> {\n  if (gradlePath) {\n    const gradleContents = await fs.readFile(path.join(androidDir, gradlePath), 'utf8');\n    const match = gradleContents.match(/namespace\\s*[=]*\\s*[\"'](.+?)[\"']/);\n    if (match) {\n      return match[1];\n    }\n  }\n  if (manifestPath) {\n    const manifestContents = await fs.readFile(path.join(androidDir, manifestPath), 'utf8');\n    const match = manifestContents.match(/package=\"(.+?)\"/);\n    if (match) {\n      return match[1];\n    }\n  }\n  return null;\n}\n\n/**\n * Parse the Java or Kotlin class name to for `ReactPackage` or `(Base|Turbo)ReactPackage`.\n */\nexport async function parseNativePackageClassNameAsync(\n  packageRoot: string,\n  androidDir: string\n): Promise<string | null> {\n  const matched = await globMatchFunctorFirstAsync(\n    '**/*Package.{java,kt}',\n    matchNativePackageClassName,\n    { cwd: androidDir }\n  );\n  if (matched) {\n    return matched;\n  }\n\n  // Early return if the module is an Expo module\n  if (await fileExistsAsync(path.join(packageRoot, 'expo-module.config.json'))) {\n    return null;\n  }\n\n  return await globMatchFunctorFirstAsync('**/*.{java,kt}', matchNativePackageClassName, {\n    cwd: androidDir,\n  });\n}\n\nlet lazyReactPackageRegex: RegExp | null = null;\nlet lazyTurboReactPackageRegex: RegExp | null = null;\nexport function matchNativePackageClassName(_filePath: string, contents: Buffer): string | null {\n  const fileContents = contents.toString();\n\n  // [0] Match ReactPackage\n  if (!lazyReactPackageRegex) {\n    lazyReactPackageRegex =\n      /class\\s+(\\w+[^(\\s]*)[\\s\\w():]*(\\s+implements\\s+|:)[\\s\\w():,]*[^{]*ReactPackage/;\n  }\n  const matchReactPackage = fileContents.match(lazyReactPackageRegex);\n  if (matchReactPackage) {\n    return matchReactPackage[1];\n  }\n\n  // [1] Match (Base|Turbo)ReactPackage\n  if (!lazyTurboReactPackageRegex) {\n    lazyTurboReactPackageRegex =\n      /class\\s+(\\w+[^(\\s]*)[\\s\\w():]*(\\s+extends\\s+|:)[\\s\\w():,]*[^{]*(Base|Turbo)ReactPackage/;\n  }\n  const matchTurboReactPackage = fileContents.match(lazyTurboReactPackageRegex);\n  if (matchTurboReactPackage) {\n    return matchTurboReactPackage[1];\n  }\n\n  return null;\n}\n\nexport async function parseLibraryNameAsync(\n  androidDir: string,\n  packageJson: any\n): Promise<string | null> {\n  // [0] `codegenConfig.name` from package.json\n  if (packageJson.codegenConfig?.name) {\n    return packageJson.codegenConfig.name;\n  }\n\n  const libraryNameRegExp = /libraryName = [\"'](.+)[\"']/;\n  const gradlePath = path.join(androidDir, 'build.gradle');\n  // [1] `libraryName` from build.gradle\n  if (await fileExistsAsync(gradlePath)) {\n    const buildGradleContents = await fs.readFile(gradlePath, 'utf8');\n    const match = buildGradleContents.match(libraryNameRegExp);\n    if (match) {\n      return match[1];\n    }\n  }\n\n  // [2] `libraryName` from build.gradle.kts\n  const gradleKtsPath = path.join(androidDir, 'build.gradle.kts');\n  if (await fileExistsAsync(gradleKtsPath)) {\n    const buildGradleContents = await fs.readFile(gradleKtsPath, 'utf8');\n    const match = buildGradleContents.match(libraryNameRegExp);\n    if (match) {\n      return match[1];\n    }\n  }\n\n  return null;\n}\n\nexport async function parseComponentDescriptorsAsync(\n  packageRoot: string,\n  packageJson: any\n): Promise<string[]> {\n  const jsRoot = packageJson?.codegenConfig?.jsSrcsDir\n    ? path.join(packageRoot, packageJson.codegenConfig.jsSrcsDir)\n    : packageRoot;\n  const results = await globMatchFunctorAllAsync(\n    '**/*.{js,jsx,ts,tsx}',\n    matchComponentDescriptors,\n    {\n      cwd: jsRoot,\n      ignore: ['**/node_modules/**'],\n    }\n  );\n\n  // Filter out duplicates as it happens that libraries contain multiple outputs due to package publishing.\n  // TODO: consider using \"codegenConfig\" to avoid this.\n  return Array.from(new Set(results));\n}\n\nlet lazyCodegenComponentRegex: RegExp | null = null;\nfunction matchComponentDescriptors(filePath: string, contents: Buffer): string | null {\n  const fileContents = contents.toString();\n\n  if (!lazyCodegenComponentRegex) {\n    lazyCodegenComponentRegex =\n      /codegenNativeComponent(<.*>)?\\s*\\(\\s*[\"'`](\\w+)[\"'`](,?[\\s\\S]+interfaceOnly:\\s*(\\w+))?/m;\n  }\n  const match = fileContents.match(lazyCodegenComponentRegex);\n  if (!(match?.[4] === 'true') && match?.[2]) {\n    return `${match[2]}ComponentDescriptor`;\n  }\n  return null;\n}\n\nexport async function findGradleAndManifestAsync({\n  androidDir,\n  isLibrary,\n}: {\n  androidDir: string;\n  isLibrary: boolean;\n}): Promise<{ gradle: string | null; manifest: string | null }> {\n  const globExcludes = [\n    'node_modules/**',\n    '**/build/**',\n    '**/debug/**',\n    'Examples/**',\n    'examples/**',\n    '**/Pods/**',\n    '**/sdks/hermes/android/**',\n    '**/src/androidTest/**',\n    '**/src/test/**',\n  ];\n  const gradlePattern = isLibrary ? 'build.gradle{,.kts}' : 'app/build.gradle{,.kts}';\n  const [manifests, gradles] = await Promise.all([\n    glob('**/AndroidManifest.xml', { cwd: androidDir, ignore: globExcludes }),\n    glob(gradlePattern, { cwd: androidDir, ignore: globExcludes }),\n  ]);\n  const manifest =\n    manifests.find((manifest) => manifest.includes('src/main/')) ??\n    manifests.sort((a, b) => a.localeCompare(b))[0];\n  const gradle = gradles.sort((a, b) => a.localeCompare(b))[0];\n  return { gradle: gradle || null, manifest: manifest || null };\n}\n"]}