{"name": "@expo/fingerprint", "version": "0.12.4", "description": "A library to generate a fingerprint from a React Native project", "main": "build/index.js", "types": "build/index.d.ts", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module", "test:e2e": "yarn run prepare && expo-module test --config e2e/jest.config.js", "typecheck": "expo-module typecheck", "watch": "expo-module tsc --watch"}, "bin": {"fingerprint": "bin/cli.js"}, "files": ["bin", "build", "cli/build"], "keywords": ["expo", "react-native", "fingerprint", "hash", "node"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/fingerprint"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/fingerprint#readme", "dependencies": {"@expo/spawn-async": "^1.7.2", "arg": "^5.0.2", "chalk": "^4.1.2", "debug": "^4.3.4", "find-up": "^5.0.0", "getenv": "^1.0.0", "minimatch": "^9.0.0", "p-limit": "^3.1.0", "resolve-from": "^5.0.0", "semver": "^7.6.0"}, "devDependencies": {"@types/find-up": "^4.0.0", "expo-module-scripts": "^4.1.6", "glob": "^10.4.2", "require-from-string": "^2.0.2", "temp-dir": "^2.0.0"}, "gitHead": "84355076bc31a356aa3d23257f387f221885f53d"}