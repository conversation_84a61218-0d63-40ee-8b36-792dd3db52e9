{"version": 3, "names": ["createNavigatorFactory", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useNavigationBuilder", "jsx", "_jsx", "StackNavigator", "props", "state", "descriptors", "NavigationContent", "children", "routes", "index", "key", "render", "createStackNavigator"], "sourceRoot": "../../../src", "sources": ["__stubs__/createStackNavigator.tsx"], "mappings": ";;AAAA,SACEA,sBAAsB,EAKtBC,WAAW,EAEXC,oBAAoB,QACf,wBAAwB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAEhC,MAAMC,cAAc,GAClBC,KAOC,IACE;EACH,MAAM;IAAEC,KAAK;IAAEC,WAAW;IAAEC;EAAkB,CAAC,GAAGP,oBAAoB,CACpED,WAAW,EACXK,KACF,CAAC;EAED,oBACEF,IAAA,CAACK,iBAAiB;IAAAC,QAAA,EACfF,WAAW,CAACD,KAAK,CAACI,MAAM,CAACJ,KAAK,CAACK,KAAK,CAAC,CAACC,GAAG,CAAC,CAACC,MAAM,CAAC;EAAC,CACnC,CAAC;AAExB,CAAC;AAED,OAAO,SAASC,oBAAoBA,CAAA,EAUjC;EACD,OAAOf,sBAAsB,CAACK,cAAc,CAAC,CAAC,CAAC;AACjD", "ignoreList": []}