/* default.css */

/*

body
{
	font: 12px "Lucida Grande", Tahoma, Arial, Helvetica, sans-serif;
	width: 800px;
}

.header
{
	clear: both;
	background-color: #ccc;
	padding: 8px;
}

h1
{
	font-size: 150%;
	font-weight: bold;
	padding: 0;
	margin: 1em 0 0 .3em;
}

hr
{
	border: none 0;
	border-top: 1px solid #7F8FB1;
	height: 1px;
}

pre.code
{
	display: block;
	padding: 8px;
	border: 1px dashed #ccc;
}

#index
{
	margin-top: 24px;
	float: left;
	width: 160px;
	position: absolute;
	left: 8px;
	background-color: #F3F3F3;
	padding: 8px;
}

#content
{
	margin-left: 190px;
	width: 600px;
}

.classList
{
	list-style-type: none;
	padding: 0;
	margin: 0 0 0 8px;
	font-family: arial, sans-serif;
	font-size: 1em;
	overflow: auto;
}

.classList li
{
	padding: 0;
	margin: 0 0 8px 0;
}

.summaryTable { width: 100%; }

h1.classTitle
{
	font-size:170%;
	line-height:130%;
}

h2 { font-size: 110%; }
caption, div.sectionTitle
{
	background-color: #7F8FB1;
	color: #fff;
	font-size:130%;
	text-align: left;
	padding: 2px 6px 2px 6px;
	border: 1px #7F8FB1 solid;
}

div.sectionTitle { margin-bottom: 8px; }
.summaryTable thead { display: none; }

.summaryTable td
{
	vertical-align: top;
	padding: 4px;
	border-bottom: 1px #7F8FB1 solid;
	border-right: 1px #7F8FB1 solid;
	border-left: 1px #7F8FB1 solid;
}

.summaryTable td.attributes
{
	border-left: 1px #7F8FB1 solid;
	width: 140px;
	text-align: right;
}

td.attributes, .fixedFont
{
	line-height: 15px;
	color: #002EBE;
	font-family: "Courier New",Courier,monospace;
	font-size: 13px;
}

.summaryTable td.nameDescription
{
	text-align: left;
	font-size: 13px;
	line-height: 15px;
}

.summaryTable td.nameDescription, .description
{
	line-height: 15px;
	padding: 4px;
	padding-left: 4px;
}

.summaryTable { margin-bottom: 8px; }

ul.inheritsList
{
	list-style: square;
	margin-left: 20px;
	padding-left: 0;
}

.detailList {
	margin-left: 20px; 
	line-height: 15px;
}
.detailList dt { margin-left: 20px; }

.detailList .heading
{
	font-weight: bold;
	padding-bottom: 6px;
	margin-left: 0;
}

.light, td.attributes, .light a:link, .light a:visited
{
	color: #777;
	font-style: italic;
}

.fineprint
{
	text-align: right;
	font-size: 10px;
}

*/

/* Copied from styles.css generated by Github Pages */

@import url(https://fonts.googleapis.com/css?family=Lato:300italic,700italic,300,700);

body {
  padding:50px;
  font:14px/1.5 Lato, "Helvetica Neue", Helvetica, Arial, sans-serif;
  color:#777;
  font-weight:300;
}

h1, h2, h3, h4, h5, h6 {
  color:#222;
  margin:0 0 20px;
}

p, ul, ol, table, pre, dl {
  margin:0 0 20px;
}

h1, h2, h3 {
  line-height:1.1;
}

h1 {
  font-size:28px;
}

h2 {
  color:#393939;
}

h3, h4, h5, h6 {
  color:#494949;
}

a {
  color:#39c;
  font-weight:400;
  text-decoration:none;
}

a small {
  font-size:11px;
  color:#777;
  margin-top:-0.6em;
  display:block;
}

.wrapper {
  width:860px;
  margin:0 auto;
}

blockquote {
  border-left:1px solid #e5e5e5;
  margin:0;
  padding:0 0 0 20px;
  font-style:italic;
}

code, pre {
  font-family:Monaco, Bitstream Vera Sans Mono, Lucida Console, Terminal;
  color:#333;
  font-size:12px;
}

pre {
  padding:8px 15px;
  background: #f8f8f8;  
  border-radius:5px;
  border:1px solid #e5e5e5;
  overflow-x: auto;
}

table {
  width:100%;
  border-collapse:collapse;
}

th, td {
  text-align:left;
  padding:5px 10px;
  border-bottom:1px solid #e5e5e5;
}

dt {
  color:#444;
  font-weight:700;
}

th {
  color:#444;
}

img {
  max-width:100%;
}

header {
  width:270px;
  float:left;
  position:fixed;
}

header ul {
  list-style:none;
  padding:0;
  
  /*background: #eee;
  background: -moz-linear-gradient(top, #f8f8f8 0%, #dddddd 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f8f8f8), color-stop(100%,#dddddd));
  background: -webkit-linear-gradient(top, #f8f8f8 0%,#dddddd 100%);
  background: -o-linear-gradient(top, #f8f8f8 0%,#dddddd 100%);
  background: -ms-linear-gradient(top, #f8f8f8 0%,#dddddd 100%);
  background: linear-gradient(top, #f8f8f8 0%,#dddddd 100%);
  
  border-radius:5px;
  border:1px solid #d2d2d2;
  box-shadow:inset #fff 0 1px 0, inset rgba(0,0,0,0.03) 0 -1px 0;*/
  width:270px;
}

header li {
  width:89px;
  /*float:left;
  border-right:1px solid #d2d2d2;
  height:40px;*/
}

header ul a {
  line-height:1;
  font-size:11px;
  color:#999;
  display:block;
  text-align:center;
  padding-top:6px;
  /*height:40px;*/
}

strong {
  color:#222;
  font-weight:700;
}

/*header ul li + li {
  width:88px;
  border-left:1px solid #fff;
}

header ul li + li + li {
  border-right:none;
  width:89px;
}*/

header ul a strong {
  font-size:14px;
  display:block;
  color:#222;
}

section {
  width:500px;
  float:right;
  padding-bottom:50px;
}

small {
  font-size:11px;
}

hr {
  border:0;
  background:#e5e5e5;
  height:1px;
  margin:0 0 20px;
}

footer {
  width:270px;
  float:left;
  position:fixed;
  bottom:50px;
}

@media print, screen and (max-width: 960px) {
  
  div.wrapper {
    width:auto;
    margin:0;
  }
  
  header, section, footer {
    float:none;
    position:static;
    width:auto;
  }
  
  header {
    /*padding-right:320px;*/
    padding: 0;
  }
  
  section {
    border:1px solid #e5e5e5;
    border-width:1px 0;
    padding:20px 0;
    margin:0 0 20px;
  }
  
  header a small {
    display:inline;
  }
  
  header ul {
    position:static;
    height:40px;
    width: auto;
  }

  header ul li {
    float: left;
  }
}

@media print, screen and (max-width: 720px) {
  body {
    word-wrap:break-word;
  }
  
  header {
    padding:0;
  }
  
  header ul, header p.view {
    position:static;
  }
  
  pre, code {
    word-wrap:normal;
  }
}

@media print, screen and (max-width: 480px) {
  body {
    padding:15px;
  }
  
  /*header ul {
    display:none;
  }*/
}

@media print {
  body {
    padding:0.4in;
    font-size:12pt;
    color:#444;
  }
}
