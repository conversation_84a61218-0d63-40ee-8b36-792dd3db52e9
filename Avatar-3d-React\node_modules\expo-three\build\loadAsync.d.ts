import './polyfillTextureLoader.fx';
import { ProgressCallback } from './loading.types';
export declare function loadBasicModelAsync(options: {
    uri: string;
    onProgress?: ProgressCallback;
    onAssetRequested: any;
    loader?: any;
    LoaderClass: any;
}): Promise<unknown>;
export default function loadAsync(res: any, onProgress?: ProgressCallback, onAssetRequested?: (...args: any[]) => any): Promise<any>;
//# sourceMappingURL=loadAsync.d.ts.map