{"version": 3, "file": "loaderClassForExtension.js", "sourceRoot": "", "sources": ["../src/loaderClassForExtension.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,0CAA0C,CAAC;AACzE,OAAO,EAAE,UAAU,EAAE,MAAM,uCAAuC,CAAC;AACnE,OAAO,EAAE,SAAS,EAAE,MAAM,sCAAsC,CAAC;AACjE,OAAO,EAAE,SAAS,EAAE,MAAM,sCAAsC,CAAC;AAEjE,SAAS,YAAY,CAAC,GAAW;IAC/B,MAAM,gBAAgB,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAY,CAAC;IACxD,OAAO,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,GAAW;IAC3C,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;IACpC,OAAO,uBAAuB,CAAC,SAAS,CAAC,CAAC;AAC5C,CAAC;AAED,MAAM,UAAU,uBAAuB,CAAC,SAAiB;IACvD,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;QAClC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;IAC9D,CAAC;IACD,QAAQ,SAAS,CAAC,WAAW,EAAE,EAAE,CAAC;QAChC,KAAK,KAAK,CAAC;QACX,KAAK,MAAM;YACT,OAAO,UAAU,CAAC;QACpB,KAAK,KAAK;YACR,OAAO,SAAS,CAAC;QACnB,KAAK,KAAK;YACR,OAAO,SAAS,CAAC;QACnB,KAAK,KAAK;YACR,OAAO,aAAa,CAAC;QACvB;YACE,MAAM,IAAI,KAAK,CACb,8DAA8D;gBAC5D,SAAS,CACZ,CAAC;IACN,CAAC;AACH,CAAC", "sourcesContent": ["import { <PERSON>ladaLoader } from 'three/examples/jsm/loaders/ColladaLoader';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { MTLLoader } from 'three/examples/jsm/loaders/MTLLoader';\nimport { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader';\n\nfunction getExtension(uri: string): string {\n  const lastUriComponent = uri.split('.').pop() as string;\n  return lastUriComponent.split('?')[0].split('#')[0];\n}\n\nexport function loaderClassForUri(uri: string): string {\n  const extension = getExtension(uri);\n  return loaderClassForExtension(extension);\n}\n\nexport function loaderClassForExtension(extension: string): any {\n  if (typeof extension !== 'string') {\n    throw new Error('Supplied extension is not a valid string');\n  }\n  switch (extension.toLowerCase()) {\n    case 'glb':\n    case 'gltf':\n      return GLTFLoader;\n    case 'obj':\n      return OBJLoader;\n    case 'mtl':\n      return MTLLoader;\n    case 'dae':\n      return ColladaLoader;\n    default:\n      throw new Error(\n        'ExpoTHREE.loaderClassForExtension(): Unrecognized file type ' +\n          extension\n      );\n  }\n}\n"]}