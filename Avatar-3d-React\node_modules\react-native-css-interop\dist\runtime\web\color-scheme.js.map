{"version": 3, "file": "color-scheme.js", "sourceRoot": "", "sources": ["../../../src/runtime/web/color-scheme.ts"], "names": [], "mappings": ";;;AAAA,+CAA6E;AAE7E,yCAA8C;AAC9C,8CAA2C;AAC3C,6CAA0C;AAE1C,IAAI,UAAU,GAAG,yBAAU,CAAC;AAC5B,IAAI,kBAAuD,CAAC;AAE5D,MAAM,YAAY,GAAG,uBAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAEpD,IAAI,QAA4B,CAAC;AACjC,IAAI,aAAiC,CAAC;AACtC,IAAI,YAAY,GAAiC,SAAS,CAAC;AAE3D,IAAI,YAAY,EAAE,CAAC;IACjB,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACtC,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACpB,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAEzB,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;QACzB,YAAY;YACV,QAAQ,IAAI,UAAU,CAAC,MAAM;gBAC7B,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,QAAQ,CAC3D,aAAa,CACd;gBACC,CAAC,CAAC,MAAM;gBACR,CAAC,CAAC,OAAO,CAAC;IAChB,CAAC;AACH,CAAC;KAAM,IAAI,QAAQ,IAAI,UAAU,EAAE,CAAC;IAGlC,MAAM,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5E,IAAI,gBAAgB,CAAC,UAAU,CAAC,EAAE,QAAQ;QAExC,MAAM,YAAY,GAAG,uBAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACpD,IAAI,CAAC,YAAY;YAAE,OAAO;QAG1B,QAAQ,CAAC,UAAU,EAAE,CAAC;QACtB,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACtC,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACpB,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACzB,mBAAW,CAAC,GAAG,CACb,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,QAAQ,CAC3D,aAAa,CACd;YACC,CAAC,CAAC,MAAM;YACR,CAAC,CAAC,QAAQ,CACb,CAAC;IACJ,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;AAC/E,CAAC;AAED,MAAM,iBAAiB,GAAG,IAAA,uBAAU,EAClC,UAAU,CAAC,cAAc,EAAE,IAAI,OAAO,CACvC,CAAC;AAEF,MAAM,qBAAqB,GAAG,IAAA,uBAAU,EACtC,YAAY,EACZ,EAAE,QAAQ,EAAE,iBAAiB,EAAE,CAChC,CAAC;AAEW,QAAA,WAAW,GAAG;IACzB,GAAG,CAAC,KAAkC;QACpC,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CACb,oHAAoH,CACrH,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CACb,sEAAsE,CACvE,CAAC;QACJ,CAAC;QAED,IAAI,KAAK,KAAK,QAAQ,EAAE,CAAC;YACvB,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACvC,CAAC;aAAM,CAAC;YACN,qBAAqB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC;QAED,IAAI,aAAa,EAAE,CAAC;YAClB,IAAI,KAAK,KAAK,MAAM,EAAE,CAAC;gBACrB,UAAU,CAAC,MAAM,EAAE,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,CACvD,aAAa,CACd,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,UAAU,CAAC,MAAM,EAAE,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,CAC1D,aAAa,CACd,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IACD,GAAG,EAAE,qBAAqB,CAAC,GAAG;IAC9B,MAAM;QACJ,IAAI,OAAO,GAAG,qBAAqB,CAAC,GAAG,EAAE,CAAC;QAC1C,IAAI,OAAO,KAAK,SAAS;YAAE,OAAO,GAAG,UAAU,CAAC,cAAc,EAAE,IAAI,OAAO,CAAC;QAC5E,mBAAW,CAAC,GAAG,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IAC1D,CAAC;IACD,CAAC,uBAAc,CAAC,EAAE,CAAC,UAA6B,EAAE,EAAE;QAClD,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACrC,wBAAwB,CAAC,UAAU,CAAC,CAAC;IACvC,CAAC;CACF,CAAC;AAEF,SAAS,wBAAwB,CAAC,WAA8B;IAC9D,UAAU,GAAG,WAAW,CAAC;IACzB,kBAAkB,EAAE,MAAM,EAAE,CAAC;IAC7B,kBAAkB,GAAG,UAAU,CAAC,iBAAiB,CAAC,CAAC,KAAK,EAAE,EAAE;QAC1D,IAAI,uBAAQ,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;YACvC,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,IAAI,OAAO,CAAC,CAAC;QACtD,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AACD,wBAAwB,CAAC,UAAU,CAAC,CAAC"}