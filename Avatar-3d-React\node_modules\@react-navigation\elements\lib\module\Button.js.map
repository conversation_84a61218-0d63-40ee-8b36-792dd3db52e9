{"version": 3, "names": ["useLinkProps", "useTheme", "Color", "React", "Platform", "StyleSheet", "PlatformPressable", "Text", "jsx", "_jsx", "BUTTON_RADIUS", "<PERSON><PERSON>", "props", "ButtonLink", "ButtonBase", "screen", "params", "action", "href", "rest", "variant", "color", "customColor", "android_ripple", "style", "children", "colors", "fonts", "primary", "backgroundColor", "textColor", "fade", "string", "isDark", "darken", "radius", "pressOpacity", "OS", "undefined", "hoverEffect", "styles", "button", "regular", "text", "create", "paddingHorizontal", "paddingVertical", "borderRadius", "fontSize", "lineHeight", "letterSpacing", "textAlign"], "sourceRoot": "../../src", "sources": ["Button.tsx"], "mappings": ";;AAAA,SAEEA,YAAY,EACZC,QAAQ,QACH,0BAA0B;AACjC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,UAAU,QAAQ,cAAc;AAEnD,SACEC,iBAAiB,QAEZ,wBAAqB;AAC5B,SAASC,IAAI,QAAQ,WAAQ;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAW9B,MAAMC,aAAa,GAAG,EAAE;AAQxB,OAAO,SAASC,MAAMA,CACpBC,KAAmD,EACnD;EACA,IAAI,QAAQ,IAAIA,KAAK,IAAI,QAAQ,IAAIA,KAAK,EAAE;IAC1C;IACA,oBAAOH,IAAA,CAACI,UAAU;MAAA,GAAKD;IAAK,CAAG,CAAC;EAClC,CAAC,MAAM;IACL,oBAAOH,IAAA,CAACK,UAAU;MAAA,GAAKF;IAAK,CAAG,CAAC;EAClC;AACF;AAEA,SAASC,UAAUA,CAAkD;EACnEE,MAAM;EACNC,MAAM;EACNC,MAAM;EACNC,IAAI;EACJ,GAAGC;AACuB,CAAC,EAAE;EAC7B;EACA,MAAMP,KAAK,GAAGZ,YAAY,CAAC;IAAEe,MAAM;IAAEC,MAAM;IAAEC,MAAM;IAAEC;EAAK,CAAC,CAAC;EAE5D,oBAAOT,IAAA,CAACK,UAAU;IAAA,GAAKK,IAAI;IAAA,GAAMP;EAAK,CAAG,CAAC;AAC5C;AAEA,SAASE,UAAUA,CAAC;EAClBM,OAAO,GAAG,QAAQ;EAClBC,KAAK,EAAEC,WAAW;EAClBC,cAAc;EACdC,KAAK;EACLC,QAAQ;EACR,GAAGN;AACY,CAAC,EAAE;EAClB,MAAM;IAAEO,MAAM;IAAEC;EAAM,CAAC,GAAG1B,QAAQ,CAAC,CAAC;EAEpC,MAAMoB,KAAK,GAAGC,WAAW,IAAII,MAAM,CAACE,OAAO;EAE3C,IAAIC,eAAe;EACnB,IAAIC,SAAS;EAEb,QAAQV,OAAO;IACb,KAAK,OAAO;MACVS,eAAe,GAAG,aAAa;MAC/BC,SAAS,GAAGT,KAAK;MACjB;IACF,KAAK,QAAQ;MACXQ,eAAe,GAAG3B,KAAK,CAACmB,KAAK,CAAC,CAACU,IAAI,CAAC,IAAI,CAAC,CAACC,MAAM,CAAC,CAAC;MAClDF,SAAS,GAAGT,KAAK;MACjB;IACF,KAAK,QAAQ;MACXQ,eAAe,GAAGR,KAAK;MACvBS,SAAS,GAAG5B,KAAK,CAACmB,KAAK,CAAC,CAACY,MAAM,CAAC,CAAC,GAC7B,OAAO,GACP/B,KAAK,CAACmB,KAAK,CAAC,CAACa,MAAM,CAAC,IAAI,CAAC,CAACF,MAAM,CAAC,CAAC;MACtC;EACJ;EAEA,oBACEvB,IAAA,CAACH,iBAAiB;IAAA,GACZa,IAAI;IACRI,cAAc,EAAE;MACdY,MAAM,EAAEzB,aAAa;MACrBW,KAAK,EAAEnB,KAAK,CAAC4B,SAAS,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAACC,MAAM,CAAC,CAAC;MAC3C,GAAGT;IACL,CAAE;IACFa,YAAY,EAAEhC,QAAQ,CAACiC,EAAE,KAAK,KAAK,GAAGC,SAAS,GAAG,CAAE;IACpDC,WAAW,EAAE;MAAElB,KAAK,EAAES;IAAU,CAAE;IAClCN,KAAK,EAAE,CAAC;MAAEK;IAAgB,CAAC,EAAEW,MAAM,CAACC,MAAM,EAAEjB,KAAK,CAAE;IAAAC,QAAA,eAEnDhB,IAAA,CAACF,IAAI;MAACiB,KAAK,EAAE,CAAC;QAAEH,KAAK,EAAES;MAAU,CAAC,EAAEH,KAAK,CAACe,OAAO,EAAEF,MAAM,CAACG,IAAI,CAAE;MAAAlB,QAAA,EAC7DA;IAAQ,CACL;EAAC,CACU,CAAC;AAExB;AAEA,MAAMe,MAAM,GAAGnC,UAAU,CAACuC,MAAM,CAAC;EAC/BH,MAAM,EAAE;IACNI,iBAAiB,EAAE,EAAE;IACrBC,eAAe,EAAE,EAAE;IACnBC,YAAY,EAAErC;EAChB,CAAC;EACDiC,IAAI,EAAE;IACJK,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,GAAG;IAClBC,SAAS,EAAE;EACb;AACF,CAAC,CAAC", "ignoreList": []}