{"version": 3, "file": "loadCubeTextureAsync.js", "sourceRoot": "", "sources": ["../src/loadCubeTextureAsync.ts"], "names": [], "mappings": "AAAA,OAAO,WAAW,MAAM,eAAe,CAAC;AAExC,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,oBAAoB,CAAC,OAGlD;IACC,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;IAClC,MAAM,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IACjC,OAAO,OAAO,CAAC;AACjB,CAAC", "sourcesContent": ["import CubeTexture from './CubeTexture';\n\nexport default async function loadCubeTextureAsync(options: {\n  assetForDirection;\n  directions?: string[];\n}): Promise<CubeTexture> {\n  const texture = new CubeTexture();\n  await texture.loadAsync(options);\n  return texture;\n}\n"]}