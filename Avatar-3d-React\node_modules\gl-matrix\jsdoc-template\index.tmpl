<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
	<head>
		<meta http-equiv="content-type" content="text/html; charset={+IO.encoding+}" />
		
		<title>glMatrix - Index</title>
		<meta name="generator" content="JsDoc Toolkit" />

		<meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
		
		<style type="text/css">
		{+include("static/default.css")+}
		</style>
	</head>
	
	<body>
		{+include("static/header.html")+}

		<div class="wrapper">
		
			<header id="index">
				{+publish.classesIndex+}
			</header>
			
			<section id="content">
				<h1 class="classTitle">Class Index</h1>
				
				<for each="thisClass" in="data">
					<if test="thisClass.alias != '_global_'">
						<div>
							<h2>{+(new Link().toSymbol(thisClass.alias))+}</h2>
							{+resolveLinks(summarize(thisClass.classDesc))+}
						</div>
						<hr />
					</if>
				</for>
				
			</section>

			<footer>
				<small>
					<if test="JSDOC.opt.D.copyright">&copy;{+JSDOC.opt.D.copyright+}<br /></if>
					Documentation generated by <a href="http://code.google.com/p/jsdoc-toolkit/" target="_blank">JsDoc Toolkit</a> {+JSDOC.VERSION+} on {+new Date()+}
					<br/><br/>
					Theme based on Github Pages template by <a href="https://github.com/orderedlist">orderedlist</a>
				</small>
			</footer>

		</div>
	</body>
</html>