{"version": 3, "file": "commonCodeMod.js", "names": ["insertContentsAtOffset", "srcContents", "insertion", "offset", "srcContentsLength", "length", "Error", "prefix", "substring", "suffix", "replaceContentsWithOffset", "contents", "replacement", "startOffset", "endOffset", "contents<PERSON>ength", "searchFromOffset", "source", "regexp", "target", "matchedIndex", "search"], "sources": ["../../src/utils/commonCodeMod.ts"], "sourcesContent": ["export interface CodeBlock {\n  start: number;\n  end: number;\n  code: string;\n}\n\n/**\n * Insert contents at given offset\n * @param srcContents source contents\n * @param insertion content to insert\n * @param offset `srcContents` offset to insert `insertion`\n * @returns updated contents\n */\nexport function insertContentsAtOffset(\n  srcContents: string,\n  insertion: string,\n  offset: number\n): string {\n  const srcContentsLength = srcContents.length;\n  if (offset < 0 || offset > srcContentsLength) {\n    throw new Error('Invalid parameters.');\n  }\n  if (offset === 0) {\n    return `${insertion}${srcContents}`;\n  } else if (offset === srcContentsLength) {\n    return `${srcContents}${insertion}`;\n  }\n\n  const prefix = srcContents.substring(0, offset);\n  const suffix = srcContents.substring(offset);\n  return `${prefix}${insertion}${suffix}`;\n}\n\n/**\n * Replace contents at given start and end offset\n *\n * @param contents source contents\n * @param replacement new contents to place in [startOffset:endOffset]\n * @param startOffset `contents` start offset for replacement\n * @param endOffset `contents` end offset for replacement\n * @returns updated contents\n */\nexport function replaceContentsWithOffset(\n  contents: string,\n  replacement: string,\n  startOffset: number,\n  endOffset: number\n): string {\n  const contentsLength = contents.length;\n  if (\n    startOffset < 0 ||\n    endOffset < 0 ||\n    startOffset >= contentsLength ||\n    endOffset >= contentsLength ||\n    startOffset > endOffset\n  ) {\n    throw new Error('Invalid parameters.');\n  }\n  const prefix = contents.substring(0, startOffset);\n  const suffix = contents.substring(endOffset + 1);\n  return `${prefix}${replacement}${suffix}`;\n}\n\n/**\n * String.prototype.search() with offset support\n *\n * @param source source string to search\n * @param regexp RegExp pattern to search\n * @param offset start offset of `source` to search `regexp` pattern\n * @returns The index of the first match between the regular expression and the given string, or -1 if no match was found.\n */\nexport function searchFromOffset(source: string, regexp: RegExp, offset: number): number {\n  const target = source.substring(offset);\n  const matchedIndex = target.search(regexp);\n  return matchedIndex < 0 ? matchedIndex : matchedIndex + offset;\n}\n"], "mappings": ";;;;;;;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASA,sBAAsBA,CACpCC,WAAmB,EACnBC,SAAiB,EACjBC,MAAc,EACN;EACR,MAAMC,iBAAiB,GAAGH,WAAW,CAACI,MAAM;EAC5C,IAAIF,MAAM,GAAG,CAAC,IAAIA,MAAM,GAAGC,iBAAiB,EAAE;IAC5C,MAAM,IAAIE,KAAK,CAAC,qBAAqB,CAAC;EACxC;EACA,IAAIH,MAAM,KAAK,CAAC,EAAE;IAChB,OAAO,GAAGD,SAAS,GAAGD,WAAW,EAAE;EACrC,CAAC,MAAM,IAAIE,MAAM,KAAKC,iBAAiB,EAAE;IACvC,OAAO,GAAGH,WAAW,GAAGC,SAAS,EAAE;EACrC;EAEA,MAAMK,MAAM,GAAGN,WAAW,CAACO,SAAS,CAAC,CAAC,EAAEL,MAAM,CAAC;EAC/C,MAAMM,MAAM,GAAGR,WAAW,CAACO,SAAS,CAACL,MAAM,CAAC;EAC5C,OAAO,GAAGI,MAAM,GAAGL,SAAS,GAAGO,MAAM,EAAE;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,yBAAyBA,CACvCC,QAAgB,EAChBC,WAAmB,EACnBC,WAAmB,EACnBC,SAAiB,EACT;EACR,MAAMC,cAAc,GAAGJ,QAAQ,CAACN,MAAM;EACtC,IACEQ,WAAW,GAAG,CAAC,IACfC,SAAS,GAAG,CAAC,IACbD,WAAW,IAAIE,cAAc,IAC7BD,SAAS,IAAIC,cAAc,IAC3BF,WAAW,GAAGC,SAAS,EACvB;IACA,MAAM,IAAIR,KAAK,CAAC,qBAAqB,CAAC;EACxC;EACA,MAAMC,MAAM,GAAGI,QAAQ,CAACH,SAAS,CAAC,CAAC,EAAEK,WAAW,CAAC;EACjD,MAAMJ,MAAM,GAAGE,QAAQ,CAACH,SAAS,CAACM,SAAS,GAAG,CAAC,CAAC;EAChD,OAAO,GAAGP,MAAM,GAAGK,WAAW,GAAGH,MAAM,EAAE;AAC3C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASO,gBAAgBA,CAACC,MAAc,EAAEC,MAAc,EAAEf,MAAc,EAAU;EACvF,MAAMgB,MAAM,GAAGF,MAAM,CAACT,SAAS,CAACL,MAAM,CAAC;EACvC,MAAMiB,YAAY,GAAGD,MAAM,CAACE,MAAM,CAACH,MAAM,CAAC;EAC1C,OAAOE,YAAY,GAAG,CAAC,GAAGA,YAAY,GAAGA,YAAY,GAAGjB,MAAM;AAChE", "ignoreList": []}