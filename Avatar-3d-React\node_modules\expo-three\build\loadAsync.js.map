{"version": 3, "file": "loadAsync.js", "sourceRoot": "", "sources": ["../src/loadAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAEhD,OAAO,EACL,uBAAuB,EACvB,iBAAiB,GAClB,MAAM,2BAA2B,CAAC;AACnC,OAAO,EACL,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,oBAAoB,GACrB,MAAM,2BAA2B,CAAC;AACnC,OAAO,4BAA4B,CAAC;AACpC,OAAO,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AAE9D,OAAO,YAAY,EAAE,EAAE,eAAe,EAAE,MAAM,gBAAgB,CAAC;AAC/D,OAAO,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,MAAM,SAAS,CAAC;AAEhE,MAAM,CAAC,KAAK,UAAU,mBAAmB,CAAC,OAMzC;IACC,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,gBAAgB,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;IAC3E,MAAM,OAAO,GAAG,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;IAC5C,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;QACpB,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;IACpC,CAAC;IACD,OAAO,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC;AAC5E,CAAC;AAED,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,SAAS,CACrC,GAAG,EACH,UAA6B,EAC7B,mBAA4C,cAAa,CAAC;IAE1D,MAAM,IAAI,GAAG,MAAM,YAAY,CAAC,GAAG,CAAC,CAAC;IACrC,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,MAAM,IAAI,KAAK,CACb,wFAAwF,GAAG,GAAG,CAC/F,CAAC;IACJ,CAAC;IACD,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACtB,MAAM,GAAG,GAAkB,CAAC,MAAM,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC;IAEhE,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QAChB,MAAM,IAAI,KAAK,CACb,qHAAqH,CACtH,CAAC;IACJ,CAAC;IAED,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACtB,IAAI,kBAAkB,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC;YAC3D,OAAO,gBAAgB,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;QACrC,CAAC;aAAM,IAAI,iBAAiB,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC;YACzC,OAAO,YAAY,CAAC;gBAClB,KAAK,EAAE,GAAG;gBACV,UAAU;gBACV,gBAAgB;aACjB,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,kBAAkB,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC;YACpD,MAAM,WAAW,GAAG,MAAM,oBAAoB,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC,CAAC;YACzE,MAAM,UAAU,GAAG,uBAAuB,CAAC,MAAM,CAAC,CAAC;YACnD,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;YAChC,OAAO,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAC9B,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,gBAAgB,EAAE,GAAG,EAAE,GAAG,CAAC,CACtD,CAAC;QACJ,CAAC;aAAM,IAAI,iBAAiB,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CACb,2GAA2G,CAC5G,CAAC;QACJ,CAAC;aAAM,IAAI,iBAAiB,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC;YACzC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC3B,OAAO,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,gBAAgB,EAAE,CAAC,CAAC;QACxD,CAAC;aAAM,IAAI,iBAAiB,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC;YACzC,OAAO,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,gBAAgB,EAAE,CAAC,CAAC;QACxD,CAAC;aAAM,CAAC;YACN,MAAM,WAAW,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;YAC3C,OAAO,mBAAmB,CAAC;gBACzB,GAAG,EAAE,GAAG;gBACR,UAAU;gBACV,gBAAgB;gBAChB,WAAW;aACZ,CAAC,CAAC;QACL,CAAC;IACH,CAAC;SAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC7B,MAAM,IAAI,GAAG,MAAM,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,IAAI,iBAAiB,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;gBACpE,OAAO,YAAY,CAAC;oBAClB,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,GAAG;oBACb,gBAAgB;iBACjB,CAAC,CAAC;YACL,CAAC;iBAAM,IACL,iBAAiB,CAAC,GAAG,EAAE,KAAK,CAAC;gBAC7B,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,EAC9B,CAAC;gBACD,OAAO,YAAY,CAAC;oBAClB,KAAK,EAAE,GAAG;oBACV,QAAQ,EAAE,IAAI;oBACd,gBAAgB;iBACjB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,GAAG,CAAC,CAAC;IACpD,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,6BAA6B,GAAG,IAAI,CAAC,CAAC;IACxD,CAAC;AACH,CAAC", "sourcesContent": ["import { resolveAsync } from 'expo-asset-utils';\n\nimport {\n  loaderClassForExtension,\n  loaderClassForUri,\n} from './loaderClassForExtension';\nimport {\n  loadDaeAsync,\n  loadObjAsync,\n  loadMtlAsync,\n  loadArrayBufferAsync,\n} from './loaders/loadModelsAsync';\nimport './polyfillTextureLoader.fx';\nimport { loadTextureAsync } from './loaders/loadTextureAsync';\nimport { ProgressCallback } from './loading.types';\nimport resolveAsset, { stringFromAsset } from './resolveAsset';\nimport { matchUrlExtension, matchUrlExtensions } from './utils';\n\nexport async function loadBasicModelAsync(options: {\n  uri: string;\n  onProgress?: ProgressCallback;\n  onAssetRequested: any;\n  loader?: any;\n  LoaderClass: any;\n}) {\n  const { uri, onProgress, onAssetRequested, loader, LoaderClass } = options;\n  const _loader = loader || new LoaderClass();\n  if (_loader.setPath) {\n    _loader.setPath(onAssetRequested);\n  }\n  return new Promise((res, rej) => _loader.load(uri, res, onProgress, rej));\n}\n\nexport default async function loadAsync(\n  res,\n  onProgress?: ProgressCallback,\n  onAssetRequested: (...args: any[]) => any = function () {}\n) {\n  const urls = await resolveAsset(res);\n  if (!urls) {\n    throw new Error(\n      `ExpoTHREE.loadAsync: Cannot parse undefined assets. Please pass valid resources for: ${res}.`\n    );\n  }\n  const asset = urls[0];\n  const url: string | null = (await resolveAsync(asset)).localUri;\n\n  if (url == null) {\n    throw new Error(\n      `ExpoTHREE.loadAsync: this asset couldn't be downloaded. Be sure that your app.json contains the correct extensions.`\n    );\n  }\n\n  if (urls.length === 1) {\n    if (matchUrlExtensions(url, ['jpeg', 'jpg', 'gif', 'png'])) {\n      return loadTextureAsync({ asset });\n    } else if (matchUrlExtension(url, 'dae')) {\n      return loadDaeAsync({\n        asset: url,\n        onProgress,\n        onAssetRequested,\n      });\n    } else if (matchUrlExtensions(url, ['glb', 'gltf'])) {\n      const arrayBuffer = await loadArrayBufferAsync({ uri: url, onProgress });\n      const GLTFLoader = loaderClassForExtension('gltf');\n      const loader = new GLTFLoader();\n      return new Promise((res, rej) =>\n        loader.parse(arrayBuffer, onAssetRequested, res, rej)\n      );\n    } else if (matchUrlExtension(url, 'json')) {\n      throw new Error(\n        'loadAsync: Please use ExpoTHREE.parseAsync({json}) instead, json can be loaded in lots of different ways.'\n      );\n    } else if (matchUrlExtension(url, 'obj')) {\n      console.log('loading obj');\n      return loadObjAsync({ asset: url, onAssetRequested });\n    } else if (matchUrlExtension(url, 'mtl')) {\n      return loadMtlAsync({ asset: url, onAssetRequested });\n    } else {\n      const LoaderClass = loaderClassForUri(url);\n      return loadBasicModelAsync({\n        uri: url,\n        onProgress,\n        onAssetRequested,\n        LoaderClass,\n      });\n    }\n  } else if (urls.length === 2) {\n    const urlB = await stringFromAsset(urls[1]);\n    if (urlB != null) {\n      if (matchUrlExtension(url, 'mtl') && matchUrlExtension(urlB, 'obj')) {\n        return loadObjAsync({\n          asset: urlB,\n          mtlAsset: url,\n          onAssetRequested,\n        });\n      } else if (\n        matchUrlExtension(url, 'obj') &&\n        matchUrlExtension(urlB, 'mtl')\n      ) {\n        return loadObjAsync({\n          asset: url,\n          mtlAsset: urlB,\n          onAssetRequested,\n        });\n      }\n    }\n\n    throw new Error('Unrecognized File Type: ' + url);\n  } else {\n    throw new Error('Too many arguments passed: ' + urls);\n  }\n}\n"]}