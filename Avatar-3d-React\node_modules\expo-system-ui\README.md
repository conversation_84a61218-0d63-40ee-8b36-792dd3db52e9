# expo-system-ui

**`expo-system-ui`** adds support for locking the user interface (appearance) and updating the root view background color.

## API documentation

- [Documentation for the latest stable release][docs-stable]
- [Documentation for the main branch][docs-main]

### Installation

```
npx expo install expo-system-ui
```

### Extra Setup

For bare React Native projects, ensure that you have the [native `expo` package][expo-modules] setup before continuing, this is required for all Expo modules.

## Contributing

Contributions are very welcome! Please refer to guidelines described in the [contributing guide][contributing].

[docs-main]: https://docs.expo.dev/versions/unversioned/sdk/system-ui/
[docs-stable]: https://docs.expo.dev/versions/latest/sdk/system-ui/
[contributing]: https://github.com/expo/expo#contributing
[expo-modules]: https://docs.expo.dev/bare/installing-expo-modules/
