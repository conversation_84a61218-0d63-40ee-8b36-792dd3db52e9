{"version": "4.1.23", "name": "nativewind", "description": "Use Tailwindcss in your cross-platform React Native applications", "main": "dist/index.js", "types": "dist/index.d.ts", "sideEffects": false, "keywords": ["react-native", "react", "native", "tailwind", "tailwindcss", "theme", "style"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/marklawlor/nativewind.git"}, "author": {"name": "<PERSON>", "url": "https://github.com/marklawlor"}, "homepage": "https://nativewind.dev", "bugs": {"url": "https://github.com/marklawlor/nativewind/issues"}, "engines": {"node": ">=16"}, "scripts": {"test": "jest", "test:watch": "jest --watch", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "lint": "eslint .", "prepublishOnly": "npm run build && npm test", "postpublish": "./post-publish.sh", "build": "tsc -p tsconfig.build.json", "build:watch": "tsc -p tsconfig.build.json -w", "dev": "tsc -p tsconfig.build.json --watch --preserveWatchOutput"}, "files": ["babel.js", "dist/", "src/", "expo-snack.js", "jsx-dev-runtime/", "jsx-runtime/", "metro/", "preset/", "test/", "theme/", "types.d.ts"], "dependencies": {"comment-json": "^4.2.5", "debug": "^4.3.7", "react-native-css-interop": "0.1.22"}, "devDependencies": {"@tailwindcss/container-queries": "^0.1.1", "@testing-library/react-native": "^12.0.1", "@types/jest": "^29.5.1", "@types/micromatch": "^4.0.4", "jest": "^29.3.1", "jest-expo": "^50.0.1", "prettier": "^3.3.1", "tailwindcss": "3.4.4", "tw-colors": "^3.3.1"}, "peerDependencies": {"tailwindcss": ">3.3.0"}}