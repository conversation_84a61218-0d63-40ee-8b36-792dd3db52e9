{"version": 3, "file": "GoogleMapsApiKey.js", "names": ["_Manifest", "data", "require", "_androidPlugins", "META_API_KEY", "LIB_HTTP", "withGoogleMapsApiKey", "exports", "createAndroidManifestPlugin", "setGoogleMapsApiKey", "getGoogleMapsApiKey", "config", "android", "googleMaps", "<PERSON><PERSON><PERSON><PERSON>", "androidManifest", "mainApplication", "getMainApplicationOrThrow", "addMetaDataItemToMainApplication", "addUsesLibraryItemToMainApplication", "name", "required", "removeMetaDataItemFromMainApplication", "removeUsesLibraryItemFromMainApplication"], "sources": ["../../src/android/GoogleMapsApiKey.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport {\n  addMetaDataItemToMainApplication,\n  addUsesLibraryItemToMainApplication,\n  AndroidManifest,\n  getMainApplicationOrThrow,\n  removeMetaDataItemFromMainApplication,\n  removeUsesLibraryItemFromMainApplication,\n} from './Manifest';\nimport { createAndroidManifestPlugin } from '../plugins/android-plugins';\n\nconst META_API_KEY = 'com.google.android.geo.API_KEY';\nconst LIB_HTTP = 'org.apache.http.legacy';\n\nexport const withGoogleMapsApiKey = createAndroidManifestPlugin(\n  setGoogleMapsApiKey,\n  'withGoogleMapsApiKey'\n);\n\nexport function getGoogleMapsApiKey(config: Pick<ExpoConfig, 'android'>) {\n  return config.android?.config?.googleMaps?.apiKey ?? null;\n}\n\nexport function setGoogleMapsApiKey(\n  config: Pick<ExpoConfig, 'android'>,\n  androidManifest: AndroidManifest\n) {\n  const apiKey = getGoogleMapsApiKey(config);\n  const mainApplication = getMainApplicationOrThrow(androidManifest);\n\n  if (apiKey) {\n    // If the item exists, add it back\n    addMetaDataItemToMainApplication(mainApplication, META_API_KEY, apiKey);\n    addUsesLibraryItemToMainApplication(mainApplication, {\n      name: LIB_HTTP,\n      required: false,\n    });\n  } else {\n    // Remove any existing item\n    removeMetaDataItemFromMainApplication(mainApplication, META_API_KEY);\n    removeUsesLibraryItemFromMainApplication(mainApplication, LIB_HTTP);\n  }\n\n  return androidManifest;\n}\n"], "mappings": ";;;;;;;;AAEA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAQA,SAAAE,gBAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,eAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,MAAMG,YAAY,GAAG,gCAAgC;AACrD,MAAMC,QAAQ,GAAG,wBAAwB;AAElC,MAAMC,oBAAoB,GAAAC,OAAA,CAAAD,oBAAA,GAAG,IAAAE,6CAA2B,EAC7DC,mBAAmB,EACnB,sBACF,CAAC;AAEM,SAASC,mBAAmBA,CAACC,MAAmC,EAAE;EACvE,OAAOA,MAAM,CAACC,OAAO,EAAED,MAAM,EAAEE,UAAU,EAAEC,MAAM,IAAI,IAAI;AAC3D;AAEO,SAASL,mBAAmBA,CACjCE,MAAmC,EACnCI,eAAgC,EAChC;EACA,MAAMD,MAAM,GAAGJ,mBAAmB,CAACC,MAAM,CAAC;EAC1C,MAAMK,eAAe,GAAG,IAAAC,qCAAyB,EAACF,eAAe,CAAC;EAElE,IAAID,MAAM,EAAE;IACV;IACA,IAAAI,4CAAgC,EAACF,eAAe,EAAEZ,YAAY,EAAEU,MAAM,CAAC;IACvE,IAAAK,+CAAmC,EAACH,eAAe,EAAE;MACnDI,IAAI,EAAEf,QAAQ;MACdgB,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACL;IACA,IAAAC,iDAAqC,EAACN,eAAe,EAAEZ,YAAY,CAAC;IACpE,IAAAmB,oDAAwC,EAACP,eAAe,EAAEX,QAAQ,CAAC;EACrE;EAEA,OAAOU,eAAe;AACxB", "ignoreList": []}