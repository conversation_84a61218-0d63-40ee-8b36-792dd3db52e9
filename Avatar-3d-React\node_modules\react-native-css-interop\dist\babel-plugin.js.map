{"version": 3, "file": "babel-plugin.js", "sourceRoot": "", "sources": ["../src/babel-plugin.ts"], "names": [], "mappings": ";;AAsBA,4BAwBC;AA9CD,wEAA4D;AAE5D,wCAWsB;AAEtB,MAAM,cAAc,GAAG,sBAAsB,CAAC;AAC9C,MAAM,YAAY,GAAG,0BAA0B,CAAC;AAChD,MAAM,QAAQ,GAAG,uBAAuB,CAAC;AAEzC,MAAM,gBAAgB,GACpB,sFAAsF,CAAC;AAEzF;IACE,OAAO;QACL,IAAI,EAAE,kCAAkC;QACxC,OAAO,EAAE;YACP,OAAO,CAAC,IAAuB,EAAE,KAA2B;gBAC1D,IAAI,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC1C,IAAI,aAAa,GAA4B,IAAI,CAAC;oBAClD,MAAM,qBAAqB,GAAG,GAAG,EAAE;wBACjC,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;4BAC3B,MAAM,kBAAkB,GAAG,IAAA,oCAAY,EAAC,IAAI,EAAE,YAAY,EAAE;gCAC1D,QAAQ,EAAE,QAAQ;6BACnB,CAAC,CAAC;4BACH,aAAa,GAAG,IAAA,wBAAgB,EAC9B,kBAAkB,EAClB,IAAA,kBAAU,EAAC,cAAc,CAAC,CAC3B,CAAC;wBACJ,CAAC;wBACD,OAAO,aAAa,CAAC;oBACvB,CAAC,CAAC;oBACF,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,GAAG,KAAK,EAAE,qBAAqB,EAAE,CAAC,CAAC;gBAC9D,CAAC;YACH,CAAC;SACF;KACF,CAAC;AACJ,CAAC;AAED,MAAM,OAAO,GAAG;IACd,gBAAgB,CACd,IAAgC,EAChC,KAA0E;QAE1E,IAAI,IAAA,oBAAY,EAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,EAAE,CAAC;YAChE,IAAI,aAAa,GAAG,KAAK,CAAC;YAE1B,IACE,IAAA,oBAAY,EAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;gBACjD,IAAA,oBAAY,EAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EACjD,CAAC;gBACD,aAAa,GAAG,mBAAmB,CACjC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAC7C,CAAC;YACJ,CAAC;iBAAM,IACL,IAAA,0BAAkB,EAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;gBACpC,IAAA,oBAAY,EAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;gBACzD,IAAA,oBAAY,EAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAC5D,CAAC;gBACD,aAAa,GAAG,mBAAmB,CACjC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CACpD,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,aAAa;gBAAE,OAAO;YAE3B,MAAM,aAAa,GAAG,KAAK,CAAC,qBAAqB,EAAE,CAAC;YAEpD,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IACD,UAAU,CACR,IAA0B,EAC1B,KAA0E;QAE1E,IACE,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,eAAe;YAClC,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE;YAClC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,EAC3D,CAAC;YACD,MAAM,aAAa,GAAG,KAAK,CAAC,qBAAqB,EAAE,CAAC;YACpD,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;CACF,CAAC;AAEF,SAAS,mBAAmB,CAAC,OAAiB;IAC5C,MAAM,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IAE3B,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO,KAAK,CAAC;IACf,CAAC;SAAM,IACL,IAAI,CAAC,iBAAiB,EAAE;QACxB,IAAI,CAAC,wBAAwB,EAAE;QAC/B,IAAI,CAAC,mBAAmB,EAAE;QAC1B,IAAI,CAAC,0BAA0B,EAAE,EACjC,CAAC;QACD,OAAO,CACL,IAAA,2BAAmB,EAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACzC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,OAAO,CAC5D,CAAC;IACJ,CAAC;SAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE,IAAI,IAAA,wBAAgB,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3E,IACE,IAAA,oBAAY,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;YACxD,IAAA,uBAAe,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAChE,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IACL,IAAA,oBAAY,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,wBAAwB,EAAE,CAAC;YACvE,IAAA,wBAAgB,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC7C,IAAA,oBAAY,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;YACrE,IAAA,uBAAe,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;gBACxD,KAAK,EAAE,OAAO;aACf,CAAC,EACF,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}