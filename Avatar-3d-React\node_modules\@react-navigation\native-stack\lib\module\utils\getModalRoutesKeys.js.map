{"version": 3, "names": ["getModalRouteKeys", "routes", "descriptors", "reduce", "acc", "route", "presentation", "key", "options", "length", "push"], "sourceRoot": "../../../src", "sources": ["utils/getModalRoutesKeys.ts"], "mappings": ";;AAIA,OAAO,MAAMA,iBAAiB,GAAGA,CAC/BC,MAAuB,EACvBC,WAAqC,KAErCD,MAAM,CAACE,MAAM,CAAW,CAACC,GAAG,EAAEC,KAAK,KAAK;EACtC,MAAM;IAAEC;EAAa,CAAC,GAAGJ,WAAW,CAACG,KAAK,CAACE,GAAG,CAAC,EAAEC,OAAO,IAAI,CAAC,CAAC;EAE9D,IACGJ,GAAG,CAACK,MAAM,IAAI,CAACH,YAAY,IAC5BA,YAAY,KAAK,OAAO,IACxBA,YAAY,KAAK,kBAAkB,EACnC;IACAF,GAAG,CAACM,IAAI,CAACL,KAAK,CAACE,GAAG,CAAC;EACrB;EAEA,OAAOH,GAAG;AACZ,CAAC,EAAE,EAAE,CAAC", "ignoreList": []}