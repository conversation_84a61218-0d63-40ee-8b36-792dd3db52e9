{"version": 3, "file": "Bare.js", "sourceRoot": "", "sources": ["../../src/sourcer/Bare.ts"], "names": [], "mappings": ";;;;;AAeA,gEAYC;AAED,wDAYC;AAED,4EA0BC;AAED,4DAUC;AAED,4FAqBC;AAED,4FAgCC;AAED,oFAgCC;AA5KD,oEAA2C;AAC3C,oDAA4B;AAC5B,kDAA0B;AAC1B,gEAAmC;AACnC,gDAAwB;AACxB,gEAAuC;AAEvC,kDAAgE;AAChE,+CAA4C;AAC5C,mCAAsD;AAEtD,wCAA4C;AAE5C,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,+BAA+B,CAAC,CAAC;AAEzD,KAAK,UAAU,0BAA0B,CAC9C,WAAmB,EACnB,OAA0B;IAE1B,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;QAC1C,MAAM,MAAM,GAAG,MAAM,IAAA,mCAA2B,EAAC,WAAW,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;QAC1F,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACnB,KAAK,CAAC,4BAA4B,eAAK,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YAC1D,OAAO,CAAC,MAAM,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAEM,KAAK,UAAU,sBAAsB,CAC1C,WAAmB,EACnB,OAA0B;IAE1B,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QACtC,MAAM,MAAM,GAAG,MAAM,IAAA,mCAA2B,EAAC,WAAW,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;QACtF,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACnB,KAAK,CAAC,4BAA4B,eAAK,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACtD,OAAO,CAAC,MAAM,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAEM,KAAK,UAAU,gCAAgC,CACpD,WAAmB,EACnB,OAA0B;IAE1B,IAAI,OAAO,CAAC,WAAW,GAAG,yBAAW,CAAC,qBAAqB,EAAE,CAAC;QAC5D,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,IAAI,WAAW,CAAC;IAChB,IAAI,CAAC;QACH,WAAW,GAAG,OAAO,CAAC,IAAA,sBAAW,EAAC,cAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAClF,CAAC;IAAC,OAAO,CAAU,EAAE,CAAC;QACpB,KAAK,CAAC,oCAAoC,cAAI,CAAC,OAAO,CAAC,WAAW,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;QAC1F,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,MAAM,OAAO,GAAiB,EAAE,CAAC;IACjC,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;QACxB,KAAK,CAAC,kCAAkC,eAAK,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAChE,MAAM,EAAE,GAAG,qBAAqB,CAAC;QACjC,OAAO,CAAC,IAAI,CAAC;YACX,IAAI,EAAE,UAAU;YAChB,EAAE;YACF,QAAQ,EAAE,iCAAiC,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC;YACzE,OAAO,EAAE,CAAC,EAAE,CAAC;SACd,CAAC,CAAC;IACL,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAEM,KAAK,UAAU,wBAAwB,CAAC,WAAmB,EAAE,OAA0B;IAC5F,IAAI,OAAO,CAAC,WAAW,GAAG,yBAAW,CAAC,SAAS,EAAE,CAAC;QAChD,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,MAAM,MAAM,GAAG,MAAM,IAAA,mCAA2B,EAAC,WAAW,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;IAC7F,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;QACnB,KAAK,CAAC,iBAAiB,eAAK,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QAClD,OAAO,CAAC,MAAM,CAAC,CAAC;IAClB,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAEM,KAAK,UAAU,wCAAwC,CAC5D,WAAmB,EACnB,OAA0B,EAC1B,4BAAsC;IAEtC,IAAI,4BAA4B,KAAK,IAAI,EAAE,CAAC;QAC1C,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,IAAI,CAAC;QACH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,qBAAU,EAAC,KAAK,EAAE,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC,CAAC;QAC7F,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAClC,MAAM,OAAO,GAAiB,MAAM,gCAAgC,CAAC;YACnE,MAAM;YACN,UAAU,EAAE,yBAAyB;YACrC,OAAO,EAAE,CAAC,mBAAmB,CAAC;SAC/B,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACjB,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,wDAAwD,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9E,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC;AAEM,KAAK,UAAU,wCAAwC,CAC5D,WAAmB,EACnB,OAA0B,EAC1B,4BAAsC;IAEtC,IAAI,4BAA4B,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;QACrF,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,IAAI,CAAC;QACH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,qBAAU,EACjC,MAAM,EACN;YACE,IAAA,4CAA6B,EAAC,WAAW,CAAC;YAC1C,qBAAqB;YACrB,QAAQ;YACR,YAAY;YACZ,SAAS;SACV,EACD,EAAE,GAAG,EAAE,WAAW,EAAE,CACrB,CAAC;QACF,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAClC,MAAM,OAAO,GAAiB,MAAM,gCAAgC,CAAC;YACnE,MAAM;YACN,UAAU,EAAE,iCAAiC;YAC7C,OAAO,EAAE,CAAC,0BAA0B,CAAC;YACrC,QAAQ,EAAE,SAAS;SACpB,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACjB,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,oEAAoE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1F,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC;AAEM,KAAK,UAAU,oCAAoC,CACxD,WAAmB,EACnB,OAA0B,EAC1B,4BAAsC;IAEtC,IAAI,4BAA4B,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QACjF,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,IAAI,CAAC;QACH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,qBAAU,EACjC,MAAM,EACN;YACE,IAAA,4CAA6B,EAAC,WAAW,CAAC;YAC1C,qBAAqB;YACrB,QAAQ;YACR,YAAY;YACZ,KAAK;SACN,EACD,EAAE,GAAG,EAAE,WAAW,EAAE,CACrB,CAAC;QACF,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAClC,MAAM,OAAO,GAAiB,MAAM,gCAAgC,CAAC;YACnE,MAAM;YACN,UAAU,EAAE,6BAA6B;YACzC,OAAO,EAAE,CAAC,sBAAsB,CAAC;YACjC,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACjB,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,gEAAgE,CAAC,EAAE,CAAC,CAAC,CAAC;QACtF,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC;AAED,KAAK,UAAU,gCAAgC,CAAC,EAC9C,MAAM,EACN,OAAO,EACP,UAAU,EACV,QAAQ,GAMT;IACC,MAAM,MAAM,GAAG,QAAQ;QACrB,CAAC,CAAC,yCAAyC,QAAQ,EAAE;QACrD,CAAC,CAAC,mCAAmC,CAAC;IACxC,MAAM,OAAO,GAAiB,EAAE,CAAC;IACjC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;IACxB,MAAM,iBAAiB,GAAwB,EAAE,CAAC;IAClD,KAAK,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAM,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC;QAC1E,IAAI,CAAC;YACH,mCAAmC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACnD,MAAM,QAAQ,GAAG,IAAA,kBAAW,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC3C,KAAK,CAAC,UAAU,MAAM,MAAM,eAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACnD,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;YAEjD,iBAAiB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;QACvC,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,gBAAgB,MAAM,MAAM,OAAO,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED,OAAO,CAAC,IAAI,CAAC;QACX,IAAI,EAAE,UAAU;QAChB,EAAE,EAAE,UAAU;QACd,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;QAC3C,OAAO;KACR,CAAC,CAAC;IACH,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,mCAAmC,CAAC,UAAe,EAAE,IAAY;IACxE,IAAA,gBAAM,EAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACxB,MAAM,cAAc,GAAG,UAAU,CAAC,IAAI,CAAC;IACvC,MAAM,YAAY,GAChB,sBAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;IAErF,UAAU,CAAC,IAAI,GAAG,IAAA,kBAAW,EAAC,cAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC,CAAC;IACnE,KAAK,MAAM,YAAY,IAAI,MAAM,CAAC,MAAM,CAAM,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QACpE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAM,YAAY,IAAI,EAAE,CAAC,EAAE,CAAC;YACnE,IAAI,QAAQ,CAAC;YACb,IACE,sBAAO,CAAC,QAAQ,KAAK,OAAO;gBAC5B,CAAC,gBAAgB,EAAE,yBAAyB,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAC3D,CAAC;gBACD,iDAAiD;gBACjD,qDAAqD;gBACrD,QAAQ,GAAG,KAAK,EAAE,UAAU,EAAE,CAAC,YAAY,CAAC;oBAC1C,CAAC,CAAC,IAAA,kBAAW,EAAC,cAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;oBACzC,CAAC,CAAC,KAAK,CAAC;YACZ,CAAC;iBAAM,CAAC;gBACN,QAAQ,GAAG,KAAK,EAAE,UAAU,EAAE,CAAC,cAAc,CAAC;oBAC5C,CAAC,CAAC,IAAA,kBAAW,EAAC,cAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;oBACzC,CAAC,CAAC,KAAK,CAAC;YACZ,CAAC;YAED,YAAY,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;QAC/B,CAAC;IACH,CAAC;AACH,CAAC;AAED,SAAS,iCAAiC,CACxC,OAA+B,EAC/B,OAA0B;IAE1B,IAAI,OAAO,CAAC,WAAW,GAAG,yBAAW,CAAC,8CAA8C,EAAE,CAAC;QACrF,4CAA4C;QAC5C,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,OAAO,KAAK,kBAAkB,EAAE,CAAC;YAChF,OAAO,OAAO,CAAC,OAAO,CAAC;QACzB,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,GAAG,KAAK,cAAc,EAAE,CAAC;YACpE,OAAO,OAAO,CAAC,GAAG,CAAC;QACrB,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AACjC,CAAC"}