/**
 * Object IDs for ASN.1.
 *
 * <AUTHOR>
 *
 * Copyright (c) 2010-2013 Digital Bazaar, Inc.
 */
var forge = require('./forge');

forge.pki = forge.pki || {};
var oids = module.exports = forge.pki.oids = forge.oids = forge.oids || {};

// set id to name mapping and name to id mapping
function _IN(id, name) {
  oids[id] = name;
  oids[name] = id;
}
// set id to name mapping only
function _I_(id, name) {
  oids[id] = name;
}

// algorithm OIDs
_IN('1.2.840.113549.1.1.1', 'rsaEncryption');
// Note: md2 & md4 not implemented
//_IN('1.2.840.113549.1.1.2', 'md2WithRSAEncryption');
//_IN('1.2.840.113549.1.1.3', 'md4WithRSAEncryption');
_IN('1.2.840.113549.1.1.4', 'md5WithRSAEncryption');
_IN('1.2.840.113549.1.1.5', 'sha1WithRSAEncryption');
_IN('1.2.840.113549.1.1.7', 'RSAES-OAEP');
_IN('1.2.840.113549.1.1.8', 'mgf1');
_IN('1.2.840.113549.1.1.9', 'pSpecified');
_IN('1.2.840.113549.1.1.10', 'RSASSA-PSS');
_IN('1.2.840.113549.1.1.11', 'sha256WithRSAEncryption');
_IN('1.2.840.113549.1.1.12', 'sha384WithRSAEncryption');
_IN('1.2.840.113549.1.1.13', 'sha512WithRSAEncryption');
// Edwards-curve Digital Signature Algorithm (EdDSA) Ed25519
_IN('1.3.101.112', 'EdDSA25519');

_IN('1.2.840.10040.4.3', 'dsa-with-sha1');

_IN('1.3.14.3.2.7', 'desCBC');

_IN('1.3.14.3.2.26', 'sha1');
// Deprecated equivalent of sha1WithRSAEncryption
_IN('1.3.14.3.2.29', 'sha1WithRSASignature');
_IN('2.16.840.*********.2.1', 'sha256');
_IN('2.16.840.*********.2.2', 'sha384');
_IN('2.16.840.*********.2.3', 'sha512');
_IN('2.16.840.*********.2.4', 'sha224');
_IN('2.16.840.*********.2.5', 'sha512-224');
_IN('2.16.840.*********.2.6', 'sha512-256');
_IN('1.2.840.113549.2.2', 'md2');
_IN('1.2.840.113549.2.5', 'md5');

// pkcs#7 content types
_IN('1.2.840.113549.1.7.1', 'data');
_IN('1.2.840.113549.1.7.2', 'signedData');
_IN('1.2.840.113549.1.7.3', 'envelopedData');
_IN('1.2.840.113549.1.7.4', 'signedAndEnvelopedData');
_IN('1.2.840.113549.1.7.5', 'digestedData');
_IN('1.2.840.113549.1.7.6', 'encryptedData');

// pkcs#9 oids
_IN('1.2.840.113549.1.9.1', 'emailAddress');
_IN('1.2.840.113549.1.9.2', 'unstructuredName');
_IN('1.2.840.113549.1.9.3', 'contentType');
_IN('1.2.840.113549.1.9.4', 'messageDigest');
_IN('1.2.840.113549.1.9.5', 'signingTime');
_IN('1.2.840.113549.1.9.6', 'counterSignature');
_IN('1.2.840.113549.1.9.7', 'challengePassword');
_IN('1.2.840.113549.1.9.8', 'unstructuredAddress');
_IN('1.2.840.113549.1.9.14', 'extensionRequest');

_IN('1.2.840.113549.1.9.20', 'friendlyName');
_IN('1.2.840.113549.1.9.21', 'localKeyId');
_IN('1.2.840.113549.********', 'x509Certificate');

// pkcs#12 safe bags
_IN('1.2.840.113549.*********.1', 'keyBag');
_IN('1.2.840.113549.*********.2', 'pkcs8ShroudedKeyBag');
_IN('1.2.840.113549.*********.3', 'certBag');
_IN('1.2.840.113549.*********.4', 'crlBag');
_IN('1.2.840.113549.*********.5', 'secretBag');
_IN('1.2.840.113549.*********.6', 'safeContentsBag');

// password-based-encryption for pkcs#12
_IN('1.2.840.113549.1.5.13', 'pkcs5PBES2');
_IN('1.2.840.113549.1.5.12', 'pkcs5PBKDF2');

_IN('1.2.840.113549.********', 'pbeWithSHAAnd128BitRC4');
_IN('1.2.840.113549.********', 'pbeWithSHAAnd40BitRC4');
_IN('1.2.840.113549.********', 'pbeWithSHAAnd3-KeyTripleDES-CBC');
_IN('1.2.840.113549.********', 'pbeWithSHAAnd2-KeyTripleDES-CBC');
_IN('1.2.840.113549.********', 'pbeWithSHAAnd128BitRC2-CBC');
_IN('1.2.840.113549.********', 'pbewithSHAAnd40BitRC2-CBC');

// hmac OIDs
_IN('1.2.840.113549.2.7', 'hmacWithSHA1');
_IN('1.2.840.113549.2.8', 'hmacWithSHA224');
_IN('1.2.840.113549.2.9', 'hmacWithSHA256');
_IN('1.2.840.113549.2.10', 'hmacWithSHA384');
_IN('1.2.840.113549.2.11', 'hmacWithSHA512');

// symmetric key algorithm oids
_IN('1.2.840.113549.3.7', 'des-EDE3-CBC');
_IN('2.16.840.*********.1.2', 'aes128-CBC');
_IN('2.16.840.*********.1.22', 'aes192-CBC');
_IN('2.16.840.*********.1.42', 'aes256-CBC');

// certificate issuer/subject OIDs
_IN('*******', 'commonName');
_IN('*******', 'surname');
_IN('*******', 'serialNumber');
_IN('*******', 'countryName');
_IN('*******', 'localityName');
_IN('*******', 'stateOrProvinceName');
_IN('*******', 'streetAddress');
_IN('********', 'organizationName');
_IN('********', 'organizationalUnitName');
_IN('********', 'title');
_IN('********', 'description');
_IN('********', 'businessCategory');
_IN('********', 'postalCode');
_IN('*******2', 'givenName');
_IN('*******.4.1.311.********', 'jurisdictionOfIncorporationStateOrProvinceName');
_IN('*******.4.1.311.********', 'jurisdictionOfIncorporationCountryName');

// X.509 extension OIDs
_IN('2.16.840.1.113730.1.1', 'nsCertType');
_IN('2.16.840.1.113730.1.13', 'nsComment'); // deprecated in theory; still widely used
_I_('********', 'authorityKeyIdentifier'); // deprecated, use .35
_I_('********', 'keyAttributes'); // obsolete use .37 or .15
_I_('********', 'certificatePolicies'); // deprecated, use .32
_I_('********', 'keyUsageRestriction'); // obsolete use .37 or .15
_I_('********', 'policyMapping'); // deprecated use .33
_I_('********', 'subtreesConstraint'); // obsolete use .30
_I_('********', 'subjectAltName'); // deprecated use .17
_I_('********', 'issuerAltName'); // deprecated use .18
_I_('********', 'subjectDirectoryAttributes');
_I_('*********', 'basicConstraints'); // deprecated use .19
_I_('********1', 'nameConstraints'); // deprecated use .30
_I_('********2', 'policyConstraints'); // deprecated use .36
_I_('********3', 'basicConstraints'); // deprecated use .19
_IN('********4', 'subjectKeyIdentifier');
_IN('********5', 'keyUsage');
_I_('********6', 'privateKeyUsagePeriod');
_IN('********7', 'subjectAltName');
_IN('********8', 'issuerAltName');
_IN('********9', 'basicConstraints');
_I_('********0', 'cRLNumber');
_I_('********1', 'cRLReason');
_I_('********2', 'expirationDate');
_I_('********3', 'instructionCode');
_I_('********4', 'invalidityDate');
_I_('********5', 'cRLDistributionPoints'); // deprecated use .31
_I_('********6', 'issuingDistributionPoint'); // deprecated use .28
_I_('********7', 'deltaCRLIndicator');
_I_('********8', 'issuingDistributionPoint');
_I_('********9', 'certificateIssuer');
_I_('********0', 'nameConstraints');
_IN('********1', 'cRLDistributionPoints');
_IN('********2', 'certificatePolicies');
_I_('********3', 'policyMappings');
_I_('********4', 'policyConstraints'); // deprecated use .36
_IN('********5', 'authorityKeyIdentifier');
_I_('********6', 'policyConstraints');
_IN('********7', 'extKeyUsage');
_I_('********6', 'freshestCRL');
_I_('********4', 'inhibitAnyPolicy');

// extKeyUsage purposes
_IN('*******.4.1.11129.2.4.2', 'timestampList');
_IN('*******.5.5.7.1.1', 'authorityInfoAccess');
_IN('*******.5.5.7.3.1', 'serverAuth');
_IN('*******.5.5.7.3.2', 'clientAuth');
_IN('*******.5.5.7.3.3', 'codeSigning');
_IN('*******.5.5.7.3.4', 'emailProtection');
_IN('*******.5.5.7.3.8', 'timeStamping');
