{"name": "expo-2d-context", "version": "0.0.3", "description": "A pure-JS implementation of the W3C's Canvas-2D Context API that can run on top of either Expo Graphics or a browser WebGL context.", "author": "<PERSON>", "main": "src/Expo2DContext.js", "types": "src/Expo2DContext.d.ts", "repository": {"type": "git", "url": "https://github.com/expo/expo-2d-context.git"}, "scripts": {"build": "esbuild src/exports.js --bundle --outfile=dist/bundle.js --loader:.png=dataurl", "lint": "eslint src", "test": "npm run test:copy-bundle && npm run test:serve & npm run test:run-suite", "test:run-suite": "node ./test/collateral/html/runSuite.js --base-url 'http://127.0.0.1:8080' all", "test:serve": "http-server test/collateral/html", "test:copy-bundle": "cp dist/bundle.js test/collateral/html/bundle.js"}, "eslintConfig": {"root": true, "extends": ["universe/shared/core", "universe/shared/prettier"], "env": {"browser": true}, "ignorePatterns": ["*.d.ts"]}, "prettier": {"printWidth": 100, "singleQuote": true}, "dependencies": {"adaptive-bezier-curve": "1.0.3", "adaptive-quadratic-curve": "1.0.2", "color-convert": "^1.9.3", "css-font-parser": "0.2.3", "domexception": "1.0.1", "earcut": "2.1.1", "gl-matrix": "^2.4.0", "parse-bmfont-ascii": "1.0.6", "string-format": "0.5.0", "tess2": "^1.0.0"}, "peerDependencies": {"expo-asset": "*"}, "devDependencies": {"@babel/core": "^7.13.10", "argparse": "^1.0.10", "esbuild": "^0.8.52", "eslint": "^7.22.0", "eslint-config-universe": "^7.0.1", "http-server": "^0.12.3", "prettier": "^2.2.1", "puppeteer": "^1.9.0"}}